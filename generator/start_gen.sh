#!/bin/bash

set -e  # 遇到错误时立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }
# 加载配置文件
if [ -f "../config.env" ]; then
    print_info "加载配置文件..."
    source ../config.env
else
    print_error "未找到配置文件 ../config.env"
    exit 1
fi

# ===============================================
# Python 环境配置
# ===============================================

print_info "配置Python环境..."

# 激活虚拟环境
if [ -f "${PYTHON_VENV_PATH}" ]; then
    source ${PYTHON_VENV_PATH}
    print_success "Python虚拟环境已激活"
else
    print_error "Python虚拟环境路径无效: ${PYTHON_VENV_PATH}"
    exit 1
fi

# 设置SSL证书路径
if [ -f "${SSL_CERT_PATH}" ]; then
    export REQUESTS_CA_BUNDLE=${SSL_CERT_PATH}
    print_success "SSL证书路径已设置"
else
    print_warning "SSL证书路径无效: ${SSL_CERT_PATH}"
fi

pip install pymdown-extensions playwright


# ps -ef | grep complex_agent.py | grep -v grep | awk '{print $2}' | xargs kill -9 
# ps -ef | grep loop.py | grep -v grep | awk '{print $2}' | xargs kill -9 

nohup python src/complex_agent.py > complex_agent.log 2>&1 &
nohup python src/loop.py > loop.log 2>&1 &
